name: Deploy FastAPI to Fly.io

on:
  push:
    branches: [ main, master ]
    paths:
      - 'Reflex_Chat/api/**'
      - 'requirements.txt'
      - 'fly-fastapi.toml'
  pull_request:
    branches: [ main, master ]
    paths:
      - 'Reflex_Chat/api/**'
      - 'requirements.txt'
      - 'fly-fastapi.toml'

jobs:
  deploy:
    name: Deploy FastAPI Service
    runs-on: ubuntu-latest
    
    # Only deploy on pushes to main/master, not on PRs
    if: github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Fly.io CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Deploy FastAPI to Fly.io
      run: flyctl deploy --config fly-fastapi.toml --remote-only
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Get FastAPI URL
      id: deployment
      run: |
        FASTAPI_URL=$(flyctl info --config fly-fastapi.toml --json | jq -r '.Hostname' | sed 's/^/https:\/\//')
        echo "url=$FASTAPI_URL" >> $GITHUB_OUTPUT
        echo "🚀 FastAPI deployed to: $FASTAPI_URL"

    - name: Update main app with FastAPI URL
      run: |
        echo "🔗 Updating main app with FastAPI URL..."
        flyctl secrets set FASTAPI_URL="${{ steps.deployment.outputs.url }}" --config fly.toml
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Update deployment status
      if: success()
      run: |
        echo "✅ FastAPI service deployed successfully!"
        echo "🌐 URL: ${{ steps.deployment.outputs.url }}"
