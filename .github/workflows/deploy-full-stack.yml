name: Deploy Full Stack to Fly.io

on:
  push:
    branches: [ main, master ]
  workflow_dispatch:  # Allow manual triggering

jobs:
  deploy-fastapi:
    name: Deploy FastAPI Service
    runs-on: ubuntu-latest
    outputs:
      fastapi-url: ${{ steps.get-url.outputs.url }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Fly.io CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Deploy FastAPI to Fly.io
      run: flyctl deploy --config fly-fastapi.toml --remote-only
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Get FastAPI URL
      id: get-url
      run: |
        FASTAPI_URL=$(flyctl info --config fly-fastapi.toml --json | jq -r '.Hostname' | sed 's/^/https:\/\//')
        echo "url=$FASTAPI_URL" >> $GITHUB_OUTPUT
        echo "🚀 FastAPI deployed to: $FASTAPI_URL"

  deploy-reflex:
    name: Deploy Reflex Application
    runs-on: ubuntu-latest
    needs: deploy-fastapi
    outputs:
      app-url: ${{ steps.get-url.outputs.url }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Fly.io CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Set FastAPI URL secret
      run: |
        echo "🔗 Setting FastAPI URL: ${{ needs.deploy-fastapi.outputs.fastapi-url }}"
        flyctl secrets set FASTAPI_URL="${{ needs.deploy-fastapi.outputs.fastapi-url }}" --config fly.toml
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy Reflex to Fly.io
      run: flyctl deploy --config fly.toml --remote-only
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Get Reflex App URL
      id: get-url
      run: |
        APP_URL=$(flyctl info --config fly.toml --json | jq -r '.Hostname' | sed 's/^/https:\/\//')
        echo "url=$APP_URL" >> $GITHUB_OUTPUT
        echo "🚀 Reflex app deployed to: $APP_URL"

  update-oauth-config:
    name: Update OAuth Configuration
    runs-on: ubuntu-latest
    needs: deploy-reflex
    
    steps:
    - name: Setup Fly.io CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Update OAuth redirect URI
      run: |
        APP_URL="${{ needs.deploy-reflex.outputs.app-url }}"
        echo "🔐 Setting OAuth redirect URI: ${APP_URL}/auth/callback"
        flyctl secrets set REDIRECT_URI="${APP_URL}/auth/callback" --config fly.toml
        flyctl secrets set FLY_APP_URL="$APP_URL" --config fly.toml
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

  deployment-summary:
    name: Deployment Summary
    runs-on: ubuntu-latest
    needs: [deploy-fastapi, deploy-reflex, update-oauth-config]
    if: always()
    
    steps:
    - name: Print deployment summary
      run: |
        echo "## 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### ✅ Services Deployed:" >> $GITHUB_STEP_SUMMARY
        echo "- **FastAPI Service**: ${{ needs.deploy-fastapi.outputs.fastapi-url }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Reflex Application**: ${{ needs.deploy-reflex.outputs.app-url }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 📋 Next Steps:" >> $GITHUB_STEP_SUMMARY
        echo "1. Update your Azure AD app registration with the new redirect URI:" >> $GITHUB_STEP_SUMMARY
        echo "   \`${{ needs.deploy-reflex.outputs.app-url }}/auth/callback\`" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "2. Test your application at: ${{ needs.deploy-reflex.outputs.app-url }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 🔍 Monitoring:" >> $GITHUB_STEP_SUMMARY
        echo "- View logs: \`flyctl logs --config fly.toml\`" >> $GITHUB_STEP_SUMMARY
        echo "- Check status: \`flyctl status --config fly.toml\`" >> $GITHUB_STEP_SUMMARY
