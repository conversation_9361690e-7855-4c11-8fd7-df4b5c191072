name: Deploy Reflex App to Fly.io

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  deploy:
    name: Deploy Reflex Application
    runs-on: ubuntu-latest
    
    # Only deploy on pushes to main/master, not on PRs
    if: github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Fly.io CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Deploy to Fly.io
      run: flyctl deploy --config fly.toml --remote-only
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Get deployment URL
      id: deployment
      run: |
        APP_URL=$(flyctl info --config fly.toml --json | jq -r '.Hostname' | sed 's/^/https:\/\//')
        echo "url=$APP_URL" >> $GITHUB_OUTPUT
        echo "🚀 Deployed to: $APP_URL"

    - name: Update deployment status
      if: success()
      run: |
        echo "✅ Reflex application deployed successfully!"
        echo "🌐 URL: ${{ steps.deployment.outputs.url }}"
