# GitHub Actions + Fly.io Deployment Setup

This guide will help you set up automatic deployment to Fly.io whenever you push to your GitHub repository.

## 🎯 Benefits of GitHub Integration

- ✅ **Automatic Deployment**: Deploy on every push to main/master
- ✅ **No Manual Commands**: No need to run flyctl commands manually
- ✅ **Version Control**: All deployments are tied to specific commits
- ✅ **Rollback Capability**: Easy to revert to previous versions
- ✅ **Team Collaboration**: Anyone with push access can deploy
- ✅ **Build Logs**: See deployment progress in GitHub Actions

## 📋 Setup Steps

### Step 1: Create Fly.io Applications

First, you need to create the applications on Fly.io (one-time setup):

```bash
# Create the main Reflex application
flyctl apps create reflex-chat-bruno-main

# Create the FastAPI service
flyctl apps create reflex-chat-bruno-api
```

### Step 2: Update Configuration Files

Update the app names in your configuration files:

1. **Edit `fly.toml`**:
   ```toml
   app = "reflex-chat-bruno-main"  # Replace with your chosen name
   ```

2. **Edit `fly-fastapi.toml`**:
   ```toml
   app = "reflex-chat-bruno-api"  # Replace with your chosen name
   ```

### Step 3: Set Up Database and Redis

```bash
# Create PostgreSQL database
flyctl postgres create --name reflex-chat-bruno-db --region iad

# Attach database to both apps
flyctl postgres attach reflex-chat-bruno-db --app reflex-chat-bruno-main
flyctl postgres attach reflex-chat-bruno-db --app reflex-chat-bruno-api

# Create Redis instance
flyctl redis create --name reflex-chat-bruno-redis --region iad

# Attach Redis to both apps
flyctl redis connect --app reflex-chat-bruno-main reflex-chat-bruno-redis
flyctl redis connect --app reflex-chat-bruno-api reflex-chat-bruno-redis
```

### Step 4: Get Fly.io API Token

1. **Generate API Token**:
   ```bash
   flyctl auth token
   ```
   
2. **Copy the token** (it will look like `fly_api_token_...`)

### Step 5: Configure GitHub Secrets

1. **Go to your GitHub repository**
2. **Navigate to**: Settings → Secrets and variables → Actions
3. **Click "New repository secret"**
4. **Add the following secrets**:

   | Secret Name | Value | Description |
   |-------------|-------|-------------|
   | `FLY_API_TOKEN` | `fly_api_token_...` | Your Fly.io API token |
   | `OPENAI_API_KEY` | `sk-...` | Your OpenAI API key |
   | `CLIENT_SECRET` | `your_azure_secret` | Azure AD client secret |
   | `TENANT_ID` | `your_tenant_id` | Azure AD tenant ID |
   | `CLIENT_ID` | `your_client_id` | Azure AD client ID |
   | `USER_AZURE_ID` | `your_user_id` | Your Azure user ID for seeding |

### Step 6: Set Application Secrets

Set the secrets for both applications:

```bash
# Main Reflex app secrets
flyctl secrets set \
  OPENAI_API_KEY="$OPENAI_API_KEY" \
  CLIENT_SECRET="$CLIENT_SECRET" \
  TENANT_ID="$TENANT_ID" \
  CLIENT_ID="$CLIENT_ID" \
  USER_AZURE_ID="$USER_AZURE_ID" \
  --app reflex-chat-bruno-main

# FastAPI app secrets
flyctl secrets set \
  OPENAI_API_KEY="$OPENAI_API_KEY" \
  CLIENT_SECRET="$CLIENT_SECRET" \
  TENANT_ID="$TENANT_ID" \
  CLIENT_ID="$CLIENT_ID" \
  --app reflex-chat-bruno-api
```

### Step 7: Test the Deployment

1. **Commit and push your changes**:
   ```bash
   git add .
   git commit -m "Add GitHub Actions deployment workflows"
   git push origin main
   ```

2. **Watch the deployment**:
   - Go to your GitHub repository
   - Click on "Actions" tab
   - Watch the "Deploy Full Stack to Fly.io" workflow run

3. **Check the deployment summary**:
   - The workflow will provide URLs for both services
   - Update your Azure AD app registration with the new redirect URI

## 🔄 How It Works

### Workflow Triggers

1. **Full Stack Deployment** (`deploy-full-stack.yml`):
   - Triggers on push to main/master
   - Deploys both FastAPI and Reflex apps
   - Updates OAuth configuration automatically

2. **FastAPI Only** (`deploy-fastapi.yml`):
   - Triggers when FastAPI code changes
   - Only deploys the FastAPI service
   - Updates the main app with new FastAPI URL

3. **Reflex Only** (`deploy-reflex-app.yml`):
   - Triggers on general application changes
   - Deploys the main Reflex application

### Deployment Order

1. **FastAPI Service** deploys first
2. **Main Reflex App** deploys with FastAPI URL
3. **OAuth Configuration** updates automatically
4. **Summary** shows all URLs and next steps

## 🔍 Monitoring Deployments

### GitHub Actions Dashboard
- View real-time deployment progress
- See build logs and error messages
- Get deployment URLs automatically

### Fly.io Monitoring
```bash
# Check application status
flyctl status --app reflex-chat-bruno-main
flyctl status --app reflex-chat-bruno-api

# View logs
flyctl logs --app reflex-chat-bruno-main
flyctl logs --app reflex-chat-bruno-api

# Monitor metrics
flyctl metrics --app reflex-chat-bruno-main
```

## 🚨 Troubleshooting

### Common Issues

1. **Deployment Fails**:
   - Check GitHub Actions logs
   - Verify all secrets are set correctly
   - Ensure app names match in configuration files

2. **Database Connection Issues**:
   - Verify database is attached to both apps
   - Check DATABASE_URL is set automatically

3. **Authentication Problems**:
   - Update Azure AD redirect URIs
   - Verify all OAuth secrets are set

### Manual Override

If you need to deploy manually:
```bash
# Deploy FastAPI
flyctl deploy --config fly-fastapi.toml

# Deploy main app
flyctl deploy --config fly.toml
```

## 🎉 Success!

Once set up, your deployment workflow will be:

1. **Make changes** to your code
2. **Commit and push** to main/master
3. **GitHub Actions** automatically deploys
4. **Get notified** with deployment URLs
5. **Test your application** at the provided URLs

Your Reflex application will now automatically deploy to Fly.io with excellent WebSocket support every time you push code changes!
