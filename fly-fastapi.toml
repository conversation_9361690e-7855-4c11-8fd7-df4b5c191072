# Fly.io configuration for FastAPI service
app = "reflex-chat-bruno-api"  # Change this to your preferred app name
primary_region = "iad"  # US East (Virginia) - change as needed

[build]
  dockerfile = "Reflex_Chat/api/Dockerfile.fastapi"

[env]
  PORT = "8001"
  PYTHONUNBUFFERED = "1"

[http_service]
  internal_port = 8001
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1
  processes = ["fastapi"]

  [http_service.concurrency]
    type = "connections"
    hard_limit = 25
    soft_limit = 20

  [[http_service.checks]]
    grace_period = "10s"
    interval = "30s"
    method = "GET"
    timeout = "5s"
    path = "/"

[processes]
  fastapi = "uvicorn main:app --host 0.0.0.0 --port 8001"

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 512
