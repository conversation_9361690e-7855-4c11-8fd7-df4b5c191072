# Fly.io configuration for Reflex Chat Application
app = "reflex-chat-app"
primary_region = "iad"  # US East (Virginia) - change as needed

[build]
  dockerfile = "Dockerfile"

[env]
  PORT = "8000"
  FRONTEND_PORT = "3000"
  BACKEND_PORT = "8000"
  PYTHONUNBUFFERED = "1"

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

  [http_service.concurrency]
    type = "connections"
    hard_limit = 25
    soft_limit = 20

  [[http_service.checks]]
    grace_period = "10s"
    interval = "30s"
    method = "GET"
    timeout = "5s"
    path = "/"

# WebSocket support for real-time UI updates
[[services]]
  protocol = "tcp"
  internal_port = 8000
  processes = ["app"]

  [[services.ports]]
    port = 80
    handlers = ["http"]
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ["http", "tls"]

  [services.concurrency]
    type = "connections"
    hard_limit = 25
    soft_limit = 20

  [[services.tcp_checks]]
    grace_period = "10s"
    interval = "30s"
    timeout = "5s"

[processes]
  app = "reflex run --env prod --frontend-port 3000 --backend-port 8000"

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 1024

[deploy]
  release_command = "python -c 'from Reflex_Chat.database.db import create_db_and_tables; create_db_and_tables()'"
