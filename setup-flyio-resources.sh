#!/bin/bash

# Fly.io Resource Setup Script
# This script creates the initial Fly.io applications, database, and Redis

set -e  # Exit on any error

echo "🚀 Setting up Fly.io resources for Reflex Chat Application"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if flyctl is installed
if ! command -v flyctl &> /dev/null; then
    print_error "flyctl is not installed. Please install it first:"
    echo "curl -L https://fly.io/install.sh | sh"
    exit 1
fi

# Check if user is logged in to Fly.io
if ! flyctl auth whoami &> /dev/null; then
    print_error "You are not logged in to Fly.io. Please run: flyctl auth login"
    exit 1
fi

print_status "Logged in as: $(flyctl auth whoami)"

# Get app names from configuration files
MAIN_APP=$(grep "^app = " fly.toml | cut -d'"' -f2)
API_APP=$(grep "^app = " fly-fastapi.toml | cut -d'"' -f2)

print_status "Main app name: $MAIN_APP"
print_status "API app name: $API_APP"

# Create applications
print_step "Creating Fly.io applications..."

print_status "Creating main Reflex application: $MAIN_APP"
if flyctl apps create "$MAIN_APP" 2>/dev/null; then
    print_status "✅ Main app created successfully"
else
    print_warning "Main app might already exist or name is taken"
fi

print_status "Creating FastAPI application: $API_APP"
if flyctl apps create "$API_APP" 2>/dev/null; then
    print_status "✅ FastAPI app created successfully"
else
    print_warning "FastAPI app might already exist or name is taken"
fi

# Create database
print_step "Creating PostgreSQL database..."
DB_NAME="${MAIN_APP}-db"
print_status "Creating database: $DB_NAME"

if flyctl postgres create --name "$DB_NAME" --region iad --initial-cluster-size 1 2>/dev/null; then
    print_status "✅ Database created successfully"
    
    # Attach database to both apps
    print_status "Attaching database to main app..."
    flyctl postgres attach "$DB_NAME" --app "$MAIN_APP"
    
    print_status "Attaching database to FastAPI app..."
    flyctl postgres attach "$DB_NAME" --app "$API_APP"
    
    print_status "✅ Database attached to both applications"
else
    print_warning "Database might already exist or there was an issue creating it"
fi

# Create Redis
print_step "Creating Redis instance..."
REDIS_NAME="${MAIN_APP}-redis"
print_status "Creating Redis: $REDIS_NAME"

if flyctl redis create --name "$REDIS_NAME" --region iad 2>/dev/null; then
    print_status "✅ Redis created successfully"
    
    # Connect Redis to both apps
    print_status "Connecting Redis to main app..."
    flyctl redis connect --app "$MAIN_APP" "$REDIS_NAME"
    
    print_status "Connecting Redis to FastAPI app..."
    flyctl redis connect --app "$API_APP" "$REDIS_NAME"
    
    print_status "✅ Redis connected to both applications"
else
    print_warning "Redis might already exist or there was an issue creating it"
fi

print_status "🎉 Fly.io resources setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. Get your Fly.io API token: flyctl auth token"
echo "2. Add the token to your GitHub repository secrets as FLY_API_TOKEN"
echo "3. Add your other secrets (OPENAI_API_KEY, CLIENT_SECRET, etc.) to GitHub"
echo "4. Push your code to trigger the first deployment"
echo ""
echo "🔍 Your resources:"
echo "- Main App: $MAIN_APP"
echo "- FastAPI App: $API_APP"
echo "- Database: $DB_NAME"
echo "- Redis: $REDIS_NAME"
echo ""
echo "📖 See GITHUB_DEPLOYMENT_SETUP.md for detailed setup instructions"
